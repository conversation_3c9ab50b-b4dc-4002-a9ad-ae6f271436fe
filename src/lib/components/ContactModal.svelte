<script lang="ts">
  import { createEventDispatcher, onMount, onDestroy } from "svelte"
  import { X } from "lucide-svelte"
  import {
    successNotification,
    errorNotification,
  } from "$lib/animations/micro-interactions"
  import { focusAnimation, clickFeedback } from "$lib/animations/actions"
  import {
    enhancedScale,
    enhancedFade,
  } from "$lib/animations/svelte-transitions"
  import { modalContactSchema, type ModalContactSchema } from "$lib/schemas"
  import type { ZodError } from "zod"

  export let isOpen = false

  const dispatch = createEventDispatcher()

  let formData = {
    name: "",
    email: "",
    website: "",
    description: "",
  }

  let isSubmitting = false
  let submitMessage = ""
  let submitError = ""
  let fieldErrors: Partial<Record<keyof typeof formData, string>> = {}
  let modalElement: HTMLDivElement
  let firstInputElement: HTMLInputElement
  let previouslyFocusedElement: HTMLElement | null = null
  let isComponentMounted = false

  function closeModal() {
    isOpen = false
    submitMessage = ""
    submitError = ""
    fieldErrors = {}

    // Restore focus to previously focused element
    if (previouslyFocusedElement) {
      previouslyFocusedElement.focus()
      previouslyFocusedElement = null
    }

    // Remove body scroll lock
    if (typeof document !== "undefined") {
      document.body.classList.remove("modal-open")
    }

    dispatch("close")
  }

  function resetForm() {
    formData = {
      name: "",
      email: "",
      website: "",
      description: "",
    }
    submitMessage = ""
    submitError = ""
    fieldErrors = {}
  }

  function validateForm() {
    try {
      modalContactSchema.parse(formData)
      fieldErrors = {}
      return true
    } catch (error: any) {
      fieldErrors = {}
      if (error.errors) {
        for (const err of error.errors) {
          const field = err.path[0] as keyof typeof formData
          fieldErrors[field] = err.message
        }
      }
      return false
    }
  }

  async function handleSubmit(event: Event) {
    event.preventDefault()

    // Reset previous messages
    submitMessage = ""
    submitError = ""

    // Validate form
    if (!validateForm()) {
      submitError = "Please fix the errors below and try again."
      return
    }

    isSubmitting = true

    try {
      const response = await fetch("/api/contact", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      const result = await response.json()

      if (result.success) {
        submitMessage =
          "Thank you! Your message has been sent successfully. We'll get back to you soon."
        successNotification(
          "Message sent successfully! We'll get back to you soon.",
        )
        resetForm()
        dispatch("submit", { success: true, data: result.data })

        // Auto-close modal after 2 seconds on success
        setTimeout(() => {
          if (isOpen) {
            closeModal()
          }
        }, 2000)
      } else {
        submitError =
          result.error || "Failed to send message. Please try again."
        errorNotification(
          result.error || "Failed to send message. Please try again.",
        )
      }
    } catch (error) {
      console.error("Form submission error:", error)
      submitError = "Network error. Please check your connection and try again."
      errorNotification(
        "Network error. Please check your connection and try again.",
      )
    } finally {
      isSubmitting = false
    }
  }

  function handleBackdropClick(event: MouseEvent) {
    if (event.target === event.currentTarget) {
      closeModal()
    }
  }

  // Focus management
  function trapFocus(event: KeyboardEvent) {
    if (!modalElement) return

    const focusableElements = modalElement.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
    )
    const firstElement = focusableElements[0] as HTMLElement
    const lastElement = focusableElements[
      focusableElements.length - 1
    ] as HTMLElement

    if (event.key === "Tab") {
      if (event.shiftKey) {
        if (document.activeElement === firstElement) {
          event.preventDefault()
          lastElement.focus()
        }
      } else {
        if (document.activeElement === lastElement) {
          event.preventDefault()
          firstElement.focus()
        }
      }
    }
  }

  // Global escape key listener
  function handleGlobalKeydown(event: KeyboardEvent) {
    if (isOpen && event.key === "Escape") {
      closeModal()
    }
  }

  // Handle modal opening with proper timing
  $: if (isOpen && typeof document !== "undefined" && isComponentMounted) {
    // Store previously focused element and prevent body scroll immediately
    previouslyFocusedElement = document.activeElement as HTMLElement
    document.body.classList.add("modal-open")

    // Trigger focus management after modal is rendered
    handleModalOpened()
  }

  // Handle focus management after modal is fully rendered
  function handleModalOpened() {
    // Wait for transitions to complete (300ms scale + buffer)
    setTimeout(() => {
      if (isOpen && firstInputElement) {
        firstInputElement.focus()
      }
    }, 350)
  }

  // Handle modal closing
  $: if (!isOpen && typeof document !== "undefined") {
    document.body.classList.remove("modal-open")
  }

  onMount(() => {
    isComponentMounted = true
    if (typeof document !== "undefined") {
      document.addEventListener("keydown", handleGlobalKeydown)
    }
  })

  onDestroy(() => {
    if (typeof document !== "undefined") {
      document.removeEventListener("keydown", handleGlobalKeydown)
      document.body.classList.remove("modal-open")
    }
  })
</script>

{#if isOpen}
  <!-- Modal backdrop -->
  <div
    class="fixed inset-0 z-50 flex items-center justify-center p-4"
    style="background: rgba(0, 0, 0, 0.8);"
    on:click={handleBackdropClick}
    on:keydown={trapFocus}
    role="dialog"
    aria-modal="true"
    aria-labelledby="modal-title"
    tabindex="-1"
  >
    <!-- Modal content -->
    <div
      bind:this={modalElement}
      class="relative w-full max-w-lg border-2"
      style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow-xl);"
      role="document"
    >
      <!-- Header -->
      <div
        class="flex items-center justify-between p-6 border-b-2"
        style="border-color: var(--border);"
      >
        <h2
          id="modal-title"
          class="text-2xl font-black"
          style="color: var(--foreground);"
        >
          Let's Start Your Growth Story
        </h2>
        <button
          on:click={closeModal}
          class="p-2 transition-colors hover:opacity-70"
          style="color: var(--muted-foreground);"
          aria-label="Close modal"
        >
          <X class="w-6 h-6" />
        </button>
      </div>

      <!-- Form -->
      <form on:submit={handleSubmit} class="p-6 space-y-6">
        <div>
          <label
            for="name"
            class="block text-sm font-bold mb-2"
            style="color: var(--foreground);"
          >
            Full Name *
          </label>
          <input
            bind:this={firstInputElement}
            id="name"
            type="text"
            bind:value={formData.name}
            required
            class="input-brutal w-full {fieldErrors.name
              ? 'border-red-500'
              : ''}"
            placeholder="Your full name"
            use:focusAnimation
          />
          {#if fieldErrors.name}
            <p class="text-red-500 text-sm mt-1">{fieldErrors.name}</p>
          {/if}
        </div>

        <div>
          <label
            for="email"
            class="block text-sm font-bold mb-2"
            style="color: var(--foreground);"
          >
            Email Address *
          </label>
          <input
            id="email"
            type="email"
            bind:value={formData.email}
            required
            class="input-brutal w-full {fieldErrors.email
              ? 'border-red-500'
              : ''}"
            placeholder="<EMAIL>"
            use:focusAnimation
          />
          {#if fieldErrors.email}
            <p class="text-red-500 text-sm mt-1">{fieldErrors.email}</p>
          {/if}
        </div>

        <div>
          <label
            for="website"
            class="block text-sm font-bold mb-2"
            style="color: var(--foreground);"
          >
            Company Website
          </label>
          <input
            id="website"
            type="url"
            bind:value={formData.website}
            class="input-brutal w-full {fieldErrors.website
              ? 'border-red-500'
              : ''}"
            placeholder="https://yourcompany.com"
            use:focusAnimation
          />
          {#if fieldErrors.website}
            <p class="text-red-500 text-sm mt-1">{fieldErrors.website}</p>
          {/if}
        </div>

        <div>
          <label
            for="description"
            class="block text-sm font-bold mb-2"
            style="color: var(--foreground);"
          >
            What would you like to learn or discuss? *
          </label>
          <textarea
            id="description"
            bind:value={formData.description}
            required
            rows="4"
            class="textarea-brutal w-full {fieldErrors.description
              ? 'border-red-500'
              : ''}"
            placeholder="Tell us about your marketing challenges, goals, or what you'd like to explore with our team..."
            use:focusAnimation
          ></textarea>
          {#if fieldErrors.description}
            <p class="text-red-500 text-sm mt-1">{fieldErrors.description}</p>
          {/if}
        </div>

        <!-- Success/Error Messages -->
        {#if submitMessage}
          <div class="modal-success-message">
            {submitMessage}
          </div>
        {/if}

        {#if submitError}
          <div class="modal-error-message">
            {submitError}
          </div>
        {/if}

        <div class="flex gap-4 pt-4">
          <button
            type="submit"
            disabled={isSubmitting}
            class="btn-primary flex-1 px-6 py-3 font-bold {isSubmitting
              ? 'opacity-75 cursor-not-allowed'
              : ''}"
            use:clickFeedback
          >
            {isSubmitting ? "Sending..." : "Send Message"}
          </button>
          <button
            type="button"
            on:click={closeModal}
            class="btn-secondary px-6 py-3 font-bold"
            use:clickFeedback
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  </div>
{/if}
