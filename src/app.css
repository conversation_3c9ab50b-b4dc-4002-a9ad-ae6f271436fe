@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

/* Warm Cream Theme */
:root {
  --background: hsl(48 33.3333% 97.0588%);
  --foreground: hsl(48 19.6078% 20%);
  --card: hsl(48 33.3333% 97.0588%);
  --card-foreground: hsl(60 2.5641% 7.6471%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(50.7692 19.403% 13.1373%);
  --primary: hsl(15.1111 55.5556% 52.3529%);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(46.1538 22.807% 88.8235%);
  --secondary-foreground: hsl(50.7692 8.4967% 30%);
  --muted: hsl(44 29.4118% 90%);
  --muted-foreground: hsl(50 2.3622% 50.1961%);
  --accent: hsl(46.1538 22.807% 88.8235%);
  --accent-foreground: hsl(50.7692 19.403% 13.1373%);
  --destructive: hsl(60 2.5641% 7.6471%);
  --destructive-foreground: hsl(0 0% 100%);
  --border: hsl(50 7.5% 84.3137%);
  --input: hsl(50.7692 7.9755% 68.0392%);
  --ring: hsl(15.1111 55.5556% 52.3529%);
  --chart-1: hsl(18.2813 57.1429% 43.9216%);
  --chart-2: hsl(251.4545 84.6154% 74.5098%);
  --chart-3: hsl(46.1538 28.2609% 81.9608%);
  --chart-4: hsl(256.5517 49.1525% 88.4314%);
  --chart-5: hsl(17.7778 60% 44.1176%);
  --sidebar: hsl(51.4286 25.9259% 94.7059%);
  --sidebar-foreground: hsl(60 2.521% 23.3333%);
  --sidebar-primary: hsl(15.1111 55.5556% 52.3529%);
  --sidebar-primary-foreground: hsl(0 0% 98.4314%);
  --sidebar-accent: hsl(46.1538 22.807% 88.8235%);
  --sidebar-accent-foreground: hsl(0 0% 20.3922%);
  --sidebar-border: hsl(0 0% 92.1569%);
  --sidebar-ring: hsl(0 0% 70.9804%);
  --font-sans: "Plus Jakarta Sans", sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
    "Courier New", monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: hsl(60 2.7027% 14.5098%);
  --foreground: hsl(46.1538 9.7744% 73.9216%);
  --card: hsl(60 2.7027% 14.5098%);
  --card-foreground: hsl(48 33.3333% 97.0588%);
  --popover: hsl(60 2.1277% 18.4314%);
  --popover-foreground: hsl(60 5.4545% 89.2157%);
  --primary: hsl(14.7692 63.1068% 59.6078%);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(48 33.3333% 97.0588%);
  --secondary-foreground: hsl(60 2.1277% 18.4314%);
  --muted: hsl(60 3.8462% 10.1961%);
  --muted-foreground: hsl(51.4286 8.8608% 69.0196%);
  --accent: hsl(48 10.6383% 9.2157%);
  --accent-foreground: hsl(51.4286 25.9259% 94.7059%);
  --destructive: hsl(0 84.2365% 60.1961%);
  --destructive-foreground: hsl(0 0% 100%);
  --border: hsl(60 5.0847% 23.1373%);
  --input: hsl(52.5 5.1282% 30.5882%);
  --ring: hsl(14.7692 63.1068% 59.6078%);
  --chart-1: hsl(18.2813 57.1429% 43.9216%);
  --chart-2: hsl(251.4545 84.6154% 74.5098%);
  --chart-3: hsl(48 10.6383% 9.2157%);
  --chart-4: hsl(248.2759 25.2174% 22.549%);
  --chart-5: hsl(17.7778 60% 44.1176%);
  --sidebar: hsl(30 3.3333% 11.7647%);
  --sidebar-foreground: hsl(46.1538 9.7744% 73.9216%);
  --sidebar-primary: hsl(0 0% 20.3922%);
  --sidebar-primary-foreground: hsl(0 0% 98.4314%);
  --sidebar-accent: hsl(60 3.4483% 5.6863%);
  --sidebar-accent-foreground: hsl(46.1538 9.7744% 73.9216%);
  --sidebar-border: hsl(0 0% 92.1569%);
  --sidebar-ring: hsl(0 0% 70.9804%);
  --font-sans: "Plus Jakarta Sans", sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
    "Courier New", monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    background: linear-gradient(
      135deg,
      hsl(48 33.3333% 97.0588%) 0%,
      hsl(46 25% 95%) 100%
    );
    font-family: var(--font-sans);
  }
}

/* Warm Theme Typography */
.linear-heading {
  font-family: var(--font-sans);
  font-weight: 600;
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.linear-body {
  font-family: var(--font-sans);
  font-weight: 400;
  line-height: 1.6;
  letter-spacing: -0.01em;
}

.linear-mono {
  font-family: var(--font-mono);
  font-weight: 500;
  letter-spacing: -0.01em;
}

/* Navigation - Warm Style */
.linear-nav {
  background: rgba(248, 244, 238, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border);
  box-shadow: var(--shadow-sm);
}

/* Hero Section - Warm */
.linear-hero {
  background:
    radial-gradient(
      ellipse at 20% 70%,
      hsla(15, 55%, 60%, 0.05) 0%,
      transparent 50%
    ),
    radial-gradient(
      ellipse at 80% 30%,
      hsla(46, 25%, 85%, 0.1) 0%,
      transparent 50%
    ),
    var(--background);
  position: relative;
  overflow: hidden;
}

.linear-hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(
      90deg,
      transparent 49.5%,
      rgba(0, 0, 0, 0.02) 50%,
      transparent 50.5%
    ),
    linear-gradient(
      0deg,
      transparent 49.5%,
      rgba(0, 0, 0, 0.02) 50%,
      transparent 50.5%
    );
  background-size: 60px 60px;
  pointer-events: none;
  opacity: 0.3;
}

/* Cards - Warm Inspired */
.linear-card {
  background: var(--card);
  border: 1px solid var(--border);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow);
}

.linear-card:hover {
  border-color: var(--primary);
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

/* Buttons - Warm Theme */
.linear-btn-primary {
  background: var(--primary);
  color: var(--primary-foreground);
  border: 1px solid var(--primary);
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow);
}

.linear-btn-primary:hover {
  background: hsl(15.1111 55.5556% 48%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.linear-btn-secondary {
  background: transparent;
  color: var(--foreground);
  border: 1px solid var(--border);
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-xs);
}

.linear-btn-secondary:hover {
  background: var(--muted);
  border-color: var(--primary);
  color: var(--foreground);
  box-shadow: var(--shadow);
}

/* Tags - Warm Color Usage */
.linear-tag {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-xs);
}

/* Green - Primary actions and success */
.linear-tag-green {
  background: hsl(120 30% 90%);
  border-color: hsl(120 40% 75%);
  color: hsl(120 60% 25%);
}

.linear-tag-green:hover {
  background: hsl(120 35% 85%);
  border-color: hsl(120 50% 65%);
  box-shadow: var(--shadow);
}

/* Purple - Premium features */
.linear-tag-purple {
  background: hsl(270 40% 90%);
  border-color: hsl(270 50% 75%);
  color: hsl(270 70% 30%);
}

/* Blue - Technical features (used sparingly) */
.linear-tag-blue {
  background: hsl(220 40% 90%);
  border-color: hsl(220 50% 75%);
  color: hsl(220 70% 30%);
}

/* Orange - Results and metrics (used sparingly) */
.linear-tag-orange {
  background: hsl(25 40% 90%);
  border-color: hsl(25 50% 75%);
  color: hsl(25 70% 30%);
}

/* Pink - Case studies (used very sparingly) */
.linear-tag-pink {
  background: hsl(330 40% 90%);
  border-color: hsl(330 50% 75%);
  color: hsl(330 70% 30%);
}

/* Data Visualization - Warm */
.linear-chart {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 0.75rem;
  padding: 2rem;
  box-shadow: var(--shadow);
}

.linear-metric {
  font-family: var(--font-mono);
  font-size: 2.5rem;
  font-weight: 600;
  color: var(--primary);
}

/* Thin Lines & Engineering Aesthetic - Warm */
.linear-grid {
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.04) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.04) 1px, transparent 1px);
  background-size: 24px 24px;
}

.linear-divider {
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(0, 0, 0, 0.1) 50%,
    transparent 100%
  );
  margin: 3rem 0;
}

.linear-dotted-line {
  border-top: 1px dotted rgba(0, 0, 0, 0.15);
  margin: 1.5rem 0;
}

/* Testimonials - Warm */
.linear-testimonial {
  background: var(--card);
  border-left: 2px solid var(--primary);
  padding: 1.5rem;
  border-radius: 0.5rem;
  position: relative;
  box-shadow: var(--shadow);
  border: 1px solid var(--border);
}

.linear-testimonial::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 100%;
  background: linear-gradient(180deg, var(--primary) 0%, hsl(15 55% 45%) 100%);
  box-shadow: 0 0 8px rgba(218, 136, 103, 0.3);
}

/* Team Cards - Warm */
.linear-team-card {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 1rem;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow);
}

.linear-team-card:hover {
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Lighting Effects - Warm */
.linear-glow {
  position: relative;
}

.linear-glow::before {
  content: "";
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(
    135deg,
    rgba(218, 136, 103, 0.2) 0%,
    transparent 50%,
    rgba(218, 136, 103, 0.1) 100%
  );
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.linear-glow:hover::before {
  opacity: 1;
}

/* Scroll Animations - Refined */
.linear-fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.linear-fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Section Separators - Warm */
.linear-section-separator {
  height: 1px;
  background: radial-gradient(
    ellipse at center,
    rgba(218, 136, 103, 0.3) 0%,
    transparent 70%
  );
  margin: 4rem 0;
  position: relative;
}

.linear-section-separator::before {
  content: "";
  position: absolute;
  top: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: var(--primary);
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(218, 136, 103, 0.4);
}

/* Performance Optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  will-change: transform;
}

/* Enhanced mobile performance optimizations */
@media (max-width: 768px) {
  /* GPU acceleration for animations */
  .linear-fade-in,
  .linear-card,
  .linear-nav,
  .mobile-menu {
    transform: translateZ(0);
    backface-visibility: hidden;
    will-change: transform, opacity;
  }

  /* Optimize scroll performance */
  .linear-hero {
    will-change: transform;
  }

  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Optimize touch interactions */
  button,
  a,
  [role="button"] {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* Improve font rendering on mobile */
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* Optimize images for mobile */
  img {
    max-width: 100%;
    height: auto;
    image-rendering: -webkit-optimize-contrast;
  }

  /* Optimize scrolling performance */
  * {
    -webkit-overflow-scrolling: touch;
  }

  /* Reduce layout thrashing */
  .linear-fade-in {
    contain: layout style paint;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .linear-metric {
    font-size: 2rem;
  }

  .linear-card {
    margin: 0.5rem 0;
  }

  .linear-chart {
    padding: 1.5rem;
  }

  /* Enhanced mobile form styling */
  .card-brutal {
    padding: 1.5rem !important;
    margin: 0.5rem !important;
  }

  /* Better mobile input spacing */
  .space-y-4 > * + * {
    margin-top: 1.25rem;
  }

  /* Mobile-friendly error messages */
  .flex.items-center.gap-2.p-3 {
    padding: 0.75rem;
    font-size: 0.875rem;
  }

  /* Mobile password strength indicator */
  .grid.grid-cols-2 {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}

/* ===== Brutal Design System Styles (from robynn.css) ===== */

/* Brutalist shadow utility classes */
.shadow-brutal {
  box-shadow: var(--shadow);
  border-radius: 0.5rem; /* 8px */
}

.shadow-brutal-sm {
  box-shadow: var(--shadow-sm);
  border-radius: 0.375rem; /* 6px */
}

.shadow-brutal-lg {
  box-shadow: var(--shadow-lg);
  border-radius: 0.5rem; /* 8px */
}

.shadow-brutal-xl {
  box-shadow: var(--shadow-xl);
  border-radius: 0.75rem; /* 12px */
}

/* Animation for floating blobs */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Custom solid text colors */
.gradient-text-primary {
  color: var(--primary);
}

.gradient-text-secondary {
  color: var(--primary);
}

/* Button styles with new theme */
.btn-primary {
  background: var(--primary);
  color: var(--primary-foreground);
  border: 2px solid var(--border);
  box-shadow: var(--shadow);
  transition: all 0.2s ease;
  border-radius: 0.375rem; /* 6px */
}

.btn-primary:hover {
  transform: translate(-2px, -2px);
  box-shadow: 6px 6px 0px 0px hsl(0 0% 0% / 1);
}

.btn-secondary {
  background: var(--background);
  color: var(--foreground);
  border: 2px solid var(--border);
  box-shadow: var(--shadow);
  transition: all 0.2s ease;
  border-radius: 0.375rem; /* 6px */
}

.btn-secondary:hover {
  transform: translate(-2px, -2px);
  box-shadow: 6px 6px 0px 0px hsl(0 0% 0% / 1);
  background: var(--muted);
}

/* Card styles with new theme */
.card-brutal {
  background: var(--card);
  border: 2px solid var(--border);
  box-shadow: var(--shadow);
  transition: all 0.2s ease;
  border-radius: 0.5rem; /* 8px */
}

.card-brutal:hover {
  transform: translate(-2px, -2px);
  box-shadow: 6px 6px 0px 0px hsl(0 0% 0% / 1);
}

/* Dashboard mockup styles */
.dashboard-card {
  background: var(--card);
  border: 2px solid var(--border);
  box-shadow: var(--shadow-lg);
  border-radius: 0.5rem; /* 8px */
}

.metric-card {
  border: 2px solid var(--border);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
  border-radius: 0.375rem; /* 6px */
}

.metric-card:hover {
  transform: translate(-1px, -1px);
  box-shadow: 3px 3px 0px 0px hsl(0 0% 0% / 1);
}

/* Progress bar styles */
.progress-bar {
  background: var(--muted);
  border: 1px solid var(--border);
  border-radius: 0.25rem; /* 4px */
}

.progress-fill {
  background: linear-gradient(90deg, var(--primary), var(--accent));
}

/* Navigation styles */
.nav-blur {
  background: rgba(248, 244, 238, 0.9);
  border-bottom: 2px solid var(--border);
  backdrop-filter: blur(10px);
}

/* Enhanced sticky navigation */
.nav-hidden {
  transform: translateY(-100%);
}

/* Mobile navigation optimizations */
@media (max-width: 768px) {
  .linear-nav {
    will-change: transform;
    transform: translateZ(0); /* GPU acceleration */
  }

  /* Mobile menu styling */
  .mobile-menu {
    box-shadow: var(--shadow-xl);
    backdrop-filter: blur(20px);
  }

  /* Prevent body scroll when mobile menu is open */
  body.mobile-menu-open {
    overflow: hidden;
  }

  /* Prevent body scroll when modal is open */
  body.modal-open {
    overflow: hidden;
  }

  /* Modal message styles */
  .modal-success-message {
    background: hsl(120 60% 50%);
    color: white;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 2px solid hsl(120 60% 40%);
    box-shadow: var(--shadow-sm);
  }

  .modal-error-message {
    background: hsl(0 60% 50%);
    color: white;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 2px solid hsl(0 60% 40%);
    box-shadow: var(--shadow-sm);
  }

  /* Touch target optimization - ensure minimum 44px touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Navigation buttons */
  .nav-link {
    min-height: 44px;
    padding: 12px 16px;
  }

  /* Mobile menu buttons */
  .mobile-menu nav button {
    min-height: 44px;
    padding: 12px 16px;
  }

  /* CTA buttons */
  .linear-btn-primary,
  .linear-btn-secondary {
    min-height: 44px;
    padding: 12px 24px;
  }

  /* Logo button */
  .linear-heading button {
    min-height: 44px;
    padding: 8px 0;
  }

  /* Icon buttons (hamburger, close) */
  button[aria-label*="menu"],
  button[aria-label*="Close"] {
    min-height: 44px;
    min-width: 44px;
    padding: 10px;
  }

  /* Links and anchor elements */
  a {
    min-height: 44px;
    display: inline-flex;
    align-items: center;
    padding: 8px 0;
  }

  /* Form inputs and interactive elements */
  input,
  textarea,
  select,
  button {
    min-height: 44px;
  }

  /* Ensure adequate spacing between touch targets */
  .touch-spacing > * + * {
    margin-top: 8px;
  }

  /* Hero section buttons */
  .linear-hero .linear-btn-primary,
  .linear-hero .linear-btn-secondary {
    min-height: 48px;
    padding: 14px 32px;
    font-size: 1.125rem;
  }

  /* Card interactive elements */
  .linear-card button,
  .linear-card a {
    min-height: 44px;
    padding: 12px 16px;
  }
}

/* Floating elements */
.floating-element {
  background: var(--card);
  border: 2px solid var(--border);
  box-shadow: var(--shadow-sm);
  border-radius: 0.5rem; /* 8px */
}

/* Input styles with new theme */
.input-brutal {
  background: var(--background);
  color: var(--foreground);
  border: 2px solid var(--border);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
  border-radius: 0.375rem; /* 6px */
}

.input-brutal:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px hsl(from var(--primary) h s l / 0.2);
}

.input-brutal:hover:not(:focus) {
  border-color: var(--muted-foreground);
}

/* Textarea styles with new theme */
.textarea-brutal {
  background: var(--background);
  color: var(--foreground);
  border: 2px solid var(--border);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
  border-radius: 0.375rem; /* 6px */
  resize: vertical;
  min-height: 80px;
}

.textarea-brutal:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px hsl(from var(--primary) h s l / 0.2);
}

.textarea-brutal:hover:not(:focus) {
  border-color: var(--muted-foreground);
}

/* Select and textarea styles */
select.input-brutal,
textarea.input-brutal {
  background: var(--background);
  color: var(--foreground);
  border: 2px solid var(--border);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
  border-radius: 0.375rem; /* 6px */
}

select.input-brutal:focus,
textarea.input-brutal:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px hsl(from var(--primary) h s l / 0.2);
}

/* Section backgrounds */
.section-muted {
  background: var(--muted);
  border-radius: 0.5rem; /* 8px */
}

.section-dark {
  background: var(--foreground);
  color: var(--background);
  border-radius: 0.5rem; /* 8px */
}

/* General brutal box style for any element */
.box-brutal {
  border: 2px solid var(--border);
  box-shadow: var(--shadow);
  border-radius: 0.5rem; /* 8px */
  transition: all 0.2s ease;
}

.box-brutal:hover {
  transform: translate(-2px, -2px);
  box-shadow: 6px 6px 0px 0px hsl(0 0% 0% / 1);
}

/* Typography utilities */
.font-mono {
  font-family: var(--font-mono);
}

.font-serif {
  font-family: var(--font-serif);
}

/* Robynn theme override to ensure it takes precedence */
.robynn-theme {
  font-family: "Plus Jakarta Sans", sans-serif !important;
  background: var(--background) !important;
  color: var(--foreground) !important;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* ===== Component-specific Styles ===== */

/* Custom scrollbar styles for chat containers */
.messages-container {
  scrollbar-width: thin;
  scrollbar-color: var(--border) var(--muted);
}

.messages-container::-webkit-scrollbar {
  width: 8px;
}

.messages-container::-webkit-scrollbar-track {
  background: var(--muted);
  border-left: 2px solid var(--border);
}

.messages-container::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 0;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: var(--foreground);
}

/* Modern full-page chat layout */
.modern-chat-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--background);
}

.modern-messages-area {
  flex: 1;
  width: 100%;
  max-width: 100%;
  padding: 2rem 1.5rem 12rem 1.5rem; /* Increased bottom padding for floating input */
  overflow-y: visible;
  min-height: calc(100vh - 200px); /* Ensure minimum height */
}

.modern-input-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--background);
  border-top: 2px solid var(--border);
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  background: rgba(var(--background-rgb, 255, 255, 255), 0.95);
  z-index: 50;
}

.modern-input-area-seo {
  padding: 2rem 1.5rem; /* Extra padding for SEO filters */
}

/* Auto-scroll behavior for modern chat */
.modern-messages-area {
  scroll-behavior: smooth;
}

/* Responsive design for modern chat */
@media (max-width: 768px) {
  .modern-messages-area {
    padding: 1rem 1rem 8rem 1rem;
  }

  .modern-input-area {
    padding: 1rem;
  }

  .modern-input-area-seo {
    padding: 1.5rem 1rem;
  }
}

@media (max-width: 480px) {
  .modern-messages-area {
    padding: 0.75rem 0.75rem 7rem 0.75rem;
  }

  .modern-input-area {
    padding: 0.75rem;
  }

  .modern-input-area-seo {
    padding: 1rem 0.75rem;
  }
}

/* Message container styling for modern layout */
.modern-message-container {
  max-width: 4xl;
  margin: 0 auto;
  width: 100%;
}

/* Ensure proper spacing between messages in modern layout */
.modern-message-item {
  margin-bottom: 2rem;
}

.modern-message-item:last-child {
  margin-bottom: 4rem; /* Extra space before input area */
}

/* Legacy layout for chat container (keeping for backward compatibility) */
.chat-container {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.messages-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 220px; /* Space for input area */
  overflow-y: auto;
  padding: 1.5rem;
}

.input-wrapper {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 220px;
  background: var(--card);
  border-top: 2px solid var(--border);
  padding: 1.5rem;
}

/* SEO agent specific input wrapper with more height for filters */
.input-wrapper-seo {
  height: 320px; /* Increased to accommodate filters and content */
}

/* SEO agent messages wrapper adjusted for larger input area */
.messages-wrapper-seo {
  bottom: 320px; /* Match the input wrapper height */
}

/* Spotlight-style input */
.spotlight-input-container {
  position: relative;
  border: 2px solid var(--border);
  border-radius: 0.5rem;
  background: var(--background);
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
}

.spotlight-input-container:focus-within {
  border-color: var(--primary);
  box-shadow:
    0 0 0 3px rgba(var(--primary-rgb, 59, 130, 246), 0.1),
    0 0 20px rgba(var(--primary-rgb, 59, 130, 246), 0.1),
    var(--shadow-lg);
  transform: translateY(-2px);
}

.spotlight-input {
  width: 100%;
  background: transparent;
  border: none;
  outline: none;
  font-family: "Plus Jakarta Sans", system-ui, sans-serif;
  color: var(--foreground);
  line-height: 1.6;
}

.spotlight-input::placeholder {
  color: var(--muted-foreground);
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

@keyframes placeholderFade {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.8;
  }
}

.spotlight-input:focus::placeholder {
  animation: placeholderFade 2s ease-in-out infinite;
}

.spotlight-button {
  position: absolute;
  right: 6px;
  bottom: 6px;
  padding: 0.75rem 1.5rem;
  background: var(--primary);
  color: var(--primary-foreground);
  border: 2px solid var(--border);
  border-radius: 0.375rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.spotlight-button:hover:not(:disabled) {
  transform: translate(-2px, -2px);
  box-shadow: 4px 4px 0 var(--border);
}

.spotlight-button:active:not(:disabled) {
  transform: translate(0, 0);
  box-shadow: var(--shadow-sm);
}

.spotlight-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Template cards */
.template-card {
  position: relative;
  overflow: hidden;
}

.template-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    transparent,
    rgba(var(--primary-rgb, 59, 130, 246), 0.05)
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.template-card:hover::before {
  opacity: 1;
}

.template-card:hover {
  box-shadow: 6px 6px 0 var(--border);
  border-color: var(--primary);
}

/* Prompt cards */
.prompt-card {
  position: relative;
  overflow: hidden;
}

.prompt-card:hover {
  box-shadow: 4px 4px 0 var(--border);
  border-color: var(--primary);
}

/* Formatted content styling */
.formatted-content {
  line-height: 1.7;
}

/* TL;DR summary specific formatting */
.formatted-summary {
  line-height: 1.5;
}

.formatted-summary h1,
.formatted-summary h2,
.formatted-summary h3,
.formatted-summary h4 {
  margin: 0.25rem 0;
  font-size: inherit;
  font-weight: 600;
}

.formatted-summary p {
  margin: 0.25rem 0;
  font-size: inherit;
}

.formatted-summary ul,
.formatted-summary ol {
  margin: 0.25rem 0;
  padding-left: 1rem;
}

.formatted-summary li {
  margin: 0;
}

.formatted-content h1:first-child,
.formatted-content h2:first-child,
.formatted-content h3:first-child {
  margin-top: 0;
}

.formatted-content ul,
.formatted-content ol {
  padding-left: 1.5rem;
}

.formatted-content table {
  font-size: 0.875rem;
}

.formatted-content blockquote {
  margin: 1rem 0;
}

.formatted-content pre {
  font-size: 0.8rem;
  line-height: 1.4;
}

.formatted-content code {
  font-size: 0.85em;
}

/* Custom select styling */
select {
  appearance: none;
  background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23666" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
  background-repeat: no-repeat;
  background-position: right 0.7rem center;
  background-size: 0.65rem auto;
}

/* Content animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Animation System with Motion One Support */

/* Base animation classes for Motion One integration */
.motion-safe {
  /* Only apply animations if user doesn't prefer reduced motion */
}

@media (prefers-reduced-motion: reduce) {
  .motion-safe {
    animation: none !important;
    transition: none !important;
  }

  /* Disable all Motion One animations */
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Enhanced fade-in animations */
.fade-in-up {
  opacity: 0;
  transform: translateY(20px);
}

.fade-in-down {
  opacity: 0;
  transform: translateY(-20px);
}

.fade-in-left {
  opacity: 0;
  transform: translateX(20px);
}

.fade-in-right {
  opacity: 0;
  transform: translateX(-20px);
}

.fade-in-scale {
  opacity: 0;
  transform: scale(0.95);
}

/* Stagger animation support */
.stagger-container > * {
  opacity: 0;
  transform: translateY(20px);
}

/* Parallax elements */
.parallax-element {
  will-change: transform;
  backface-visibility: hidden;
}

/* Hover animation base classes */
.hover-lift {
  transition: transform 0.2s ease-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale {
  transition: transform 0.2s ease-out;
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Loading animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Scan animation for loading states */
@keyframes scan {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-scan {
  animation: scan 2s linear infinite;
}

/* Page Transition Styles */
.page-content {
  transition: all 0.3s ease-in-out;
  will-change: transform, opacity;
}

.page-content.transitioning {
  pointer-events: none;
}

/* Elements that should animate on page load */
.animate-on-load {
  opacity: 0;
  transform: translateY(20px);
}

/* Page transition overlay */
.page-transition-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--background);
  z-index: 9999;
  pointer-events: none;
}

/* Component transition base classes */
.modal-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Stagger animation support */
.stagger-item {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.4s ease-out;
}

.stagger-item.animate {
  opacity: 1;
  transform: translateY(0);
}

/* Enhanced focus states for better transitions */
.focus-transition {
  transition: all 0.2s ease-out;
}

.focus-transition:focus {
  transform: scale(1.02);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

/* Smooth scrolling for better page transitions */
html {
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }

  .page-content,
  .modal-transition,
  .dropdown-transition,
  .stagger-item,
  .focus-transition {
    transition: none !important;
  }
}

/* Keyboard shortcut style */
kbd {
  font-family: "JetBrains Mono", "Courier New", monospace;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Mode selector active state */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--primary-rgb, 59, 130, 246), 0.2);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(var(--primary-rgb, 59, 130, 246), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--primary-rgb, 59, 130, 246), 0);
  }
}

/* Smooth transitions for mode changes */
.messages-container {
  transition: all 0.3s ease;
}

/* Enhanced input styling */
.enhanced-input {
  transition: all 0.3s ease;
}

.enhanced-input:focus {
  box-shadow:
    0 0 0 3px var(--primary),
    0 0 20px var(--primary);
  border-color: var(--primary);
}

.enhanced-input::placeholder {
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.enhanced-input:focus::placeholder {
  opacity: 0.5;
}

/* Animated icon effects */
.w-12.h-12 .animate-pulse {
  animation: iconPulse 2s infinite;
}

@keyframes iconPulse {
  0%,
  100% {
    transform: scale(1);
    filter: drop-shadow(0 0 0 var(--primary));
  }
  50% {
    transform: scale(1.05);
    filter: drop-shadow(0 0 8px var(--primary));
  }
}

/* Button hover effects */
.btn-primary:hover .animate-pulse {
  animation: iconGlow 1s infinite;
}

@keyframes iconGlow {
  0%,
  100% {
    filter: drop-shadow(0 0 4px var(--primary-foreground));
  }
  50% {
    filter: drop-shadow(0 0 12px var(--primary-foreground));
  }
}

/* Progress animations */
.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Navigation link hover effects */
.nav-link {
  position: relative;
  padding: 0.25rem 0;
  color: var(--foreground);
  background: none;
  border: none;
  cursor: pointer;
}

/* Logo button styling */
.linear-heading button {
  background: none;
  border: none;
  padding: 0;
  font: inherit;
  text-align: left;
}

.nav-link::after {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 50%;
  background-color: var(--primary);
  transition:
    width 0.3s ease,
    left 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
  left: 0;
}

.nav-link:hover {
  color: var(--primary);
}

/* Fade and blur animations */
@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(24px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes blur-in {
  0% {
    filter: blur(20px);
    opacity: 0;
  }
  100% {
    filter: blur(0);
    opacity: 1;
  }
}

.animate-fade {
  animation: fade-in 0.9s forwards;
}

.animate-blur {
  animation: blur-in 1.2s forwards;
}
